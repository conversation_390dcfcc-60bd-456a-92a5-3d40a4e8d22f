Metadata-Version: 2.1
Name: immutabledict
Version: 4.2.1
Summary: Immutable wrapper around dictionaries (a fork of frozendict)
Home-page: https://github.com/corenting/immutabledict
License: MIT
Keywords: immutable,dictionary
Author: Corentin Garcia
Author-email: <EMAIL>
Requires-Python: >=3.8
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Project-URL: Bug Tracker, https://github.com/corenting/immutabledict/issues
Project-URL: Changelog, https://github.com/corenting/immutabledict/blob/master/CHANGELOG.md
Project-URL: Documentation, https://immutabledict.corenting.fr
Project-URL: Donation, https://corenting.fr/donate
Project-URL: Repository, https://github.com/corenting/immutabledict
Description-Content-Type: text/markdown

# immutabledict

![PyPI](https://img.shields.io/pypi/v/immutabledict) ![Conda](https://img.shields.io/conda/vn/conda-forge/immutabledict) ![PyPI - Python Version](https://img.shields.io/pypi/pyversions/immutabledict)

![License](https://img.shields.io/pypi/l/immutabledict) ![Build](https://img.shields.io/github/actions/workflow/status/corenting/immutabledict/ci.yml?branch=master) ![Codecov](https://img.shields.io/codecov/c/github/corenting/immutabledict) ![PyPI - Downloads](https://img.shields.io/pypi/dm/immutabledict)

An immutable wrapper around dictionaries. immutabledict implements the complete mapping interface and can be used as a drop-in replacement for dictionaries where immutability is desired.

It's a fork of slezica's [frozendict](https://github.com/slezica/python-frozendict). This library is a pure Python, MIT-licensed alternative to the new LGPL-3.0 licensed [frozendict](https://github.com/Marco-Sulla/python-frozendict).

## Installation

Official release in [on pypi](https://pypi.org/project/immutabledict/) as `immutabledict`.

**Community-maintained** releases are available:
- On [conda-forge](https://anaconda.org/conda-forge/immutabledict) as `immutabledict`
- On [various package repositories](https://repology.org/project/python:immutabledict/versions)

## Example

```python
from immutabledict import immutabledict

my_item = immutabledict({"a": "value", "b": "other_value"})
print(my_item["a"]) # Print "value"
```

## Differences with the old original frozendict package

- Dropped support of EOL Python versions (older versions of the library may support older Python versions)
- Fixed `collections.Mapping` deprecation warning
- Typing
- [PEP 584 union operators](https://www.python.org/dev/peps/pep-0584/)
- Keep the same signature for `copy()` as `dict` (starting with immutabledict 3.0.0), don't accept extra keyword arguments.
- Added `set`, `delete` and `discard` methods

## Donations

If you wish to support this library, donations are possible [here](https://corenting.fr/donate).

