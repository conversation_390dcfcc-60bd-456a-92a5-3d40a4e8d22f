import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import folium
import webbrowser
from datetime import datetime
import calendar
import os
import xlsxwriter
from typing import Dict, List
import threading
from queue import Queue
import math


class RouteOptimizationApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Route Optimizer")
        self.root.geometry("800x600")

        # Variables
        self.outlets_data = None
        self.routes = {}
        self.schedule = {}
        self.status_var = tk.StringVar(value="Ready")
        self.progress_var = tk.DoubleVar(value=0)
        self.processing = False
        self.queue = Queue()

        self.create_widgets()
        self.layout_widgets()
        self.setup_queue_checker()

    def create_widgets(self):
        # Parameters Frame
        self.param_frame = ttk.LabelFrame(self.root, text="Parameters", padding="10")

        # Parameter Entries
        self.min_outlets_per_day = ttk.Entry(self.param_frame, state='normal')
        self.min_outlets_per_day.insert(0, "")
        self.max_outlets_per_day = ttk.Entry(self.param_frame, state='disabled')
        self.max_outlets_per_day.insert(0, "")
        self.working_days = ttk.Entry(self.param_frame, state='disabled')
        self.working_days.insert(0, "")
        self.max_distance_km = ttk.Entry(self.param_frame, state='disabled')
        self.max_distance_km.insert(0, "")

        ttk.Label(self.param_frame, text="Min outlets per day:").grid(row=0, column=0, padx=5, pady=5)
        self.min_outlets_per_day.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(self.param_frame, text="Max outlets per day:").grid(row=1, column=0, padx=5, pady=5)
        self.max_outlets_per_day.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(self.param_frame, text="Working days per week:").grid(row=2, column=0, padx=5, pady=5)
        self.working_days.grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(self.param_frame, text="Max distance between outlets (km):").grid(row=3, column=0, padx=5, pady=5)
        self.max_distance_km.grid(row=3, column=1, padx=5, pady=5)

        # Bind validation to input fields
        self.min_outlets_per_day.bind("<KeyRelease>", self.validate_min_outlets)
        self.max_outlets_per_day.bind("<KeyRelease>", self.validate_max_outlets)
        self.working_days.bind("<KeyRelease>", self.validate_working_days)
        self.max_distance_km.bind("<KeyRelease>", self.validate_max_distance)

        # Control Buttons
        self.button_frame = ttk.Frame(self.root, padding="10")
        self.load_button = ttk.Button(self.button_frame, text="Load Data", command=self.load_data, state='disabled')
        self.optimize_button = ttk.Button(self.button_frame, text="Optimize Routes",
                                          command=self.start_optimization, state='disabled')
        self.export_button = ttk.Button(self.button_frame, text="Export Results",
                                        command=self.start_export, state='disabled')

        # Progress Bar
        self.progress_frame = ttk.Frame(self.root, padding="5")
        self.progress_bar = ttk.Progressbar(self.progress_frame,
                                            variable=self.progress_var,
                                            maximum=100,
                                            mode='determinate',
                                            length=300)

        # Status Bar
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)

        # Results Text
        self.results_frame = ttk.LabelFrame(self.root, text="Results", padding="10")
        self.results_text = tk.Text(self.results_frame, height=20, width=70)
        self.scrollbar = ttk.Scrollbar(self.results_frame, orient="vertical",
                                       command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=self.scrollbar.set)

    def layout_widgets(self):
        self.param_frame.pack(fill=tk.X, padx=10, pady=5)

        self.button_frame.pack(fill=tk.X, padx=10, pady=5)
        self.load_button.pack(side=tk.LEFT, padx=5)
        self.optimize_button.pack(side=tk.LEFT, padx=5)
        self.export_button.pack(side=tk.LEFT, padx=5)

        self.progress_frame.pack(fill=tk.X, padx=10, pady=5)
        self.progress_bar.pack(fill=tk.X)

        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def validate_min_outlets(self, event):
        """Enable max_outlets_per_day if min_outlets_per_day is valid"""
        try:
            min_outlets = int(self.min_outlets_per_day.get())
            if min_outlets > 0:
                self.max_outlets_per_day.config(state='normal')
            else:
                self.max_outlets_per_day.config(state='disabled')
                self.working_days.config(state='disabled')
                self.max_distance_km.config(state='disabled')
                self.load_button.config(state='disabled')
        except ValueError:
            self.max_outlets_per_day.config(state='disabled')
            self.working_days.config(state='disabled')
            self.max_distance_km.config(state='disabled')
            self.load_button.config(state='disabled')

    def validate_max_outlets(self, event):
        """Enable working_days if max_outlets_per_day is valid"""
        try:
            max_outlets = int(self.max_outlets_per_day.get())
            min_outlets = int(self.min_outlets_per_day.get())
            if max_outlets >= min_outlets:
                self.working_days.config(state='normal')
            else:
                self.working_days.config(state='disabled')
                self.max_distance_km.config(state='disabled')
                self.load_button.config(state='disabled')
        except ValueError:
            self.working_days.config(state='disabled')
            self.max_distance_km.config(state='disabled')
            self.load_button.config(state='disabled')

    def validate_working_days(self, event):
        """Enable max_distance_km if working_days is valid"""
        try:
            working_days = int(self.working_days.get())
            if working_days > 0:
                self.max_distance_km.config(state='normal')
            else:
                self.max_distance_km.config(state='disabled')
                self.load_button.config(state='disabled')
        except ValueError:
            self.max_distance_km.config(state='disabled')
            self.load_button.config(state='disabled')

    def validate_max_distance(self, event):
        """Enable load_button if max_distance_km is valid"""
        try:
            max_distance = float(self.max_distance_km.get())
            if max_distance > 0:
                self.load_button.config(state='normal')
            else:
                self.load_button.config(state='disabled')
        except ValueError:
            self.load_button.config(state='disabled')

    def load_data(self):
        try:
            filename = filedialog.askopenfilename(
                title="Select Data File",
                filetypes=[("CSV files", "*.csv")]
            )
            if not filename:
                return

            self.queue.put(('status', "Loading data..."))
            self.queue.put(('progress', 10))

            self.outlets_data = pd.read_csv(filename)
            self.validate_data()

            self.optimize_button.config(state='normal')
            self.queue.put(('log', "Data loaded successfully"))
            self.calculate_required_reps()

            self.queue.put(('status', "Ready"))
            self.queue.put(('progress', 100))

        except Exception as e:
            self.queue.put(('error', f"Error loading data: {str(e)}"))

    def validate_data(self):
        required_columns = {'outletname', 'latitude', 'longitude'}
        missing = required_columns - set(map(str.lower, self.outlets_data.columns))
        if missing:
            raise ValueError(f"Missing required columns: {missing}")

    def calculate_required_reps(self):
        try:
            min_outlets_per_day = int(self.min_outlets_per_day.get())
            max_outlets_per_day = int(self.max_outlets_per_day.get())
            working_days = int(self.working_days.get())
            total_outlets = len(self.outlets_data)

            visits_per_week = max_outlets_per_day * working_days
            total_biweekly_visits = total_outlets * 2
            required_reps = int(np.ceil(total_biweekly_visits / (visits_per_week * 2)))

            self.queue.put(('log', f"\nAnalysis:"))
            self.queue.put(('log', f"Total outlets: {total_outlets}"))
            self.queue.put(('log', f"Visits per week per rep: {visits_per_week}"))
            self.queue.put(('log', f"Required representatives: {required_reps}"))

            return required_reps

        except ValueError as e:
            self.queue.put(('error', "Please enter valid numbers for parameters"))
            return None

    def start_optimization(self):
        """Start optimization in a separate thread"""
        if self.processing:
            return

        self.processing = True
        self.disable_buttons()
        threading.Thread(target=self.optimize_routes, daemon=True).start()

    def optimize_routes(self):
        try:
            self.queue.put(('status', "Calculating requirements..."))
            self.queue.put(('progress', 10))

            required_reps = self.calculate_required_reps()
            if not required_reps:
                self.queue.put(('complete', None))
                return

            self.queue.put(('status', "Creating clusters based on proximity..."))
            self.queue.put(('progress', 30))

            # Convert latitude and longitude to polar coordinates
            centroid = self.outlets_data[['latitude', 'longitude']].mean().values
            self.outlets_data['polar_angle'], self.outlets_data['polar_radius'] = self.cartesian_to_polar(
                self.outlets_data[['latitude', 'longitude']].values, centroid
            )

            # Cluster outlets based on polar angle
            self.outlets_data['cluster'] = self.cluster_by_angle(self.outlets_data['polar_angle'], required_reps)

            self.queue.put(('status', "Generating routes..."))
            self.queue.put(('progress', 60))

            # Create routes for each cluster
            min_outlets_per_day = int(self.min_outlets_per_day.get())
            max_outlets_per_day = int(self.max_outlets_per_day.get())
            working_days = int(self.working_days.get())

            self.routes = self.create_routes(min_outlets_per_day, max_outlets_per_day, working_days)

            self.queue.put(('status', "Creating schedule..."))
            self.queue.put(('progress', 80))

            self.create_schedule()

            self.queue.put(('status', "Optimization completed"))
            self.queue.put(('progress', 100))

            self.root.after(0, lambda: self.export_button.config(state='normal'))

        except Exception as e:
            self.queue.put(('error', f"Error during optimization: {str(e)}"))
        finally:
            self.queue.put(('complete', None))

    def cartesian_to_polar(self, coords: np.ndarray, centroid: np.ndarray) -> (np.ndarray, np.ndarray):
        """
        Convert latitude and longitude to polar coordinates (angle and radius).
        """
        # Calculate differences from centroid
        delta_lat = coords[:, 0] - centroid[0]
        delta_lon = coords[:, 1] - centroid[1]

        # Calculate radius (distance from centroid)
        radius = np.sqrt(delta_lat**2 + delta_lon**2)

        # Calculate angle (in radians)
        angle = np.arctan2(delta_lon, delta_lat)

        # Convert angle to degrees (0 to 360)
        angle = np.degrees(angle) % 360

        return angle, radius

    def cluster_by_angle(self, angles: np.ndarray, num_clusters: int) -> np.ndarray:
        """
        Cluster outlets based on their polar angle.
        """
        # Sort angles and assign clusters
        sorted_indices = np.argsort(angles)
        cluster_labels = np.zeros_like(angles, dtype=int)

        # Divide outlets into clusters based on angle
        cluster_size = len(angles) // num_clusters
        for i in range(num_clusters):
            start = i * cluster_size
            end = (i + 1) * cluster_size if i < num_clusters - 1 else len(angles)
            cluster_labels[sorted_indices[start:end]] = i

        return cluster_labels

    def create_routes(self, min_outlets_per_day: int, max_outlets_per_day: int, working_days: int) -> Dict:
        routes = {}

        for cluster in self.outlets_data['cluster'].unique():
            cluster_data = self.outlets_data[self.outlets_data['cluster'] == cluster].copy()

            # Sort outlets by radius (distance from centroid)
            cluster_data = cluster_data.sort_values(by='polar_radius')

            # Split cluster into daily groups
            daily_groups = []
            outlets_in_cluster = len(cluster_data)

            # Calculate the number of days needed for this cluster
            num_days = int(np.ceil(outlets_in_cluster / max_outlets_per_day))

            # Ensure each day has between min_outlets_per_day and max_outlets_per_day
            for day in range(num_days):
                start_idx = day * max_outlets_per_day
                end_idx = start_idx + max_outlets_per_day

                # Adjust end_idx to ensure the last group has at least min_outlets_per_day
                if day == num_days - 1:
                    end_idx = len(cluster_data)
                    if end_idx - start_idx < min_outlets_per_day:
                        # Merge the last group with the previous one
                        if len(daily_groups) > 0:
                            daily_groups[-1] = pd.concat([daily_groups[-1], cluster_data.iloc[start_idx:end_idx]])
                        continue

                daily_group = cluster_data.iloc[start_idx:end_idx]
                daily_groups.append(daily_group)

            # Ensure no group exceeds max_outlets_per_day
            final_daily_groups = []
            for group in daily_groups:
                if len(group) > max_outlets_per_day:
                    # Split the group into smaller groups
                    num_splits = int(np.ceil(len(group) / max_outlets_per_day))
                    for i in range(num_splits):
                        start = i * max_outlets_per_day
                        end = start + max_outlets_per_day
                        final_daily_groups.append(group.iloc[start:end])
                else:
                    final_daily_groups.append(group)

            routes[cluster] = final_daily_groups

            self.queue.put(('log', f"\nRepresentative {cluster + 1}:"))
            for day_idx, group in enumerate(final_daily_groups, 1):
                self.queue.put(('log', f"Day {day_idx}: {len(group)} outlets"))

        return routes

    def create_schedule(self):
        working_days = int(self.working_days.get())
        days = list(calendar.day_name)[:working_days]

        self.schedule = {}
        for rep_id, daily_routes in self.routes.items():
            rep_schedule = []
            week1_routes = daily_routes[:working_days]
            week2_routes = daily_routes[working_days:] if len(daily_routes) > working_days else week1_routes

            # Generate schedules for 4 weeks
            for week in range(4):
                if week == 0 or week == 2:  # Week 1 and Week 3
                    current_routes = week1_routes
                else:  # Week 2 and Week 4
                    current_routes = week2_routes

                for day_idx, day in enumerate(days):
                    if day_idx < len(current_routes):
                        route = current_routes[day_idx]
                        rep_schedule.append({
                            'Week': week + 1,
                            'Day': day,
                            'Outlets': route['outletname'].tolist()
                        })

            self.schedule[f'Representative {rep_id + 1}'] = rep_schedule

            # Log unique outlets per week
            self.queue.put(('log', f"\nRepresentative {rep_id + 1}:"))
            for week in range(4):
                if week == 2:  # Week 3
                    self.queue.put(('log', f"Week {week + 1}: Same as Week 1"))
                elif week == 3:  # Week 4
                    self.queue.put(('log', f"Week {week + 1}: Same as Week 2"))
                else:
                    week_schedule = [entry for entry in rep_schedule if entry['Week'] == week + 1]
                    unique_outlets = set()
                    for day_schedule in week_schedule:
                        unique_outlets.update(day_schedule['Outlets'])
                    self.queue.put(('log', f"Week {week + 1}: {len(unique_outlets)} unique outlets"))

    def start_export(self):
        """Start export in a separate thread"""
        if self.processing:
            return

        self.processing = True
        self.disable_buttons()
        threading.Thread(target=self.export_results, daemon=True).start()

    def export_results(self):
        try:
            self.queue.put(('status', "Exporting schedule..."))
            self.queue.put(('progress', 20))

            # Create the "Rep Schedule" folder
            rep_schedule_dir = "Rep Schedule"
            os.makedirs(rep_schedule_dir, exist_ok=True)

            # Export each rep's schedule to a separate Excel file
            for rep, rep_schedule in self.schedule.items():
                # Skip if no schedule is available for this representative
                if not rep_schedule:
                    self.queue.put(('log', f"No schedule found for {rep}"))
                    continue

                # Create a DataFrame for the rep's schedule
                schedule_data = []
                for entry in rep_schedule:
                    week = entry['Week']
                    day = entry['Day']
                    for outlet_name in entry['Outlets']:
                        # Get GPS coordinates for the outlet
                        outlet_data = self.outlets_data[self.outlets_data['outletname'] == outlet_name]
                        if not outlet_data.empty:
                            latitude = outlet_data['latitude'].values[0]
                            longitude = outlet_data['longitude'].values[0]
                        else:
                            latitude = "N/A"
                            longitude = "N/A"

                        schedule_data.append({
                            'Week': week,
                            'Day': day,
                            'Outlet Name': outlet_name,
                            'Latitude': latitude,
                            'Longitude': longitude
                        })

                df = pd.DataFrame(schedule_data)

                # Save the DataFrame to an Excel file
                filename = os.path.join(rep_schedule_dir, f"{rep}_schedule.xlsx")
                with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
                    df.to_excel(writer, index=False, sheet_name='Schedule')

            self.queue.put(('status', "Creating maps..."))
            self.queue.put(('progress', 60))

            # Create maps
            self.create_maps()

            self.queue.put(('log', f"\nResults exported:"))
            self.queue.put(('log', f"Schedule: {rep_schedule_dir} directory"))
            self.queue.put(('log', "Maps: route_maps directory"))
            self.queue.put(('status', "Export completed"))
            self.queue.put(('progress', 100))

        except Exception as e:
            self.queue.put(('error', f"Error exporting results: {str(e)}"))
        finally:
            self.queue.put(('complete', None))

    def create_maps(self):
        maps_dir = "route_maps"
        os.makedirs(maps_dir, exist_ok=True)

        colors = ['red', 'blue', 'green', 'purple', 'orange', 'darkred',
                  'lightred', 'darkblue', 'darkgreen', 'cadetblue', 'darkpurple']

        for rep_id, daily_routes in self.routes.items():
            # Skip if no routes are available for this representative
            if not daily_routes:
                self.queue.put(('log', f"No routes found for Representative {rep_id + 1}"))
                continue

            # Create map centered on the first outlet in the cluster
            try:
                first_outlet = daily_routes[0].iloc[0]
                m = folium.Map(
                    location=[first_outlet['latitude'], first_outlet['longitude']],
                    zoom_start=12
                )

                # Add routes for each day
                for day_idx, route in enumerate(daily_routes):
                    color = colors[day_idx % len(colors)]

                    # Add markers for each outlet
                    for _, outlet in route.iterrows():
                        folium.Marker(
                            [outlet['latitude'], outlet['longitude']],
                            popup=f"Day {day_idx + 1}: {outlet['outletname']}",
                            icon=folium.Icon(color=color)
                        ).add_to(m)

                    # Draw lines between outlets for the route
                    if len(route) > 1:
                        folium.PolyLine(
                            locations=route[['latitude', 'longitude']].values,
                            color=color,
                            weight=2.5,
                            opacity=1
                        ).add_to(m)

                map_file = os.path.join(maps_dir, f'rep_{rep_id + 1}_routes.html')
                m.save(map_file)
                webbrowser.open(f'file://{os.path.abspath(map_file)}')

            except Exception as e:
                self.queue.put(('error', f"Error creating map for Representative {rep_id + 1}: {str(e)}"))

    def disable_buttons(self):
        """Disable all buttons during processing"""
        self.load_button.config(state='disabled')
        self.optimize_button.config(state='disabled')
        self.export_button.config(state='disabled')

    def enable_buttons(self):
        """Re-enable appropriate buttons after processing"""
        self.load_button.config(state='normal')
        if self.outlets_data is not None:
            self.optimize_button.config(state='normal')
        if hasattr(self, 'schedule'):
            self.export_button.config(state='normal')

    def log_message(self, message: str):
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)

    def show_error(self, message: str):
        messagebox.showerror("Error", message)
        self.status_var.set("Error occurred")

    def setup_queue_checker(self):
        """Set up periodic queue checking for thread communication"""
        def check_queue():
            while not self.queue.empty():
                msg_type, msg = self.queue.get()
                if msg_type == 'log':
                    self.log_message(msg)
                elif msg_type == 'progress':
                    self.progress_var.set(msg)
                elif msg_type == 'status':
                    self.status_var.set(msg)
                elif msg_type == 'error':
                    self.show_error(msg)
                elif msg_type == 'complete':
                    self.processing = False
                    self.enable_buttons()

            self.root.after(100, check_queue)

        self.root.after(100, check_queue)

    def run(self):
        self.root.mainloop()


if __name__ == "__main__":
    try:
        app = RouteOptimizationApp()
        app.run()
    except Exception as e:
        print(f"Application error: {str(e)}")