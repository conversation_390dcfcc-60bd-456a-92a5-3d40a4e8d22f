import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import folium # Requires installation: pip install folium openpyxl
from datetime import datetime
import calendar
import os
import xlsxwriter # For Excel export
from typing import Dict, List, Tuple, Optional, Any
import threading
from queue import Queue, Empty as QueueEmpty
import math
from ortools.constraint_solver import routing_enums_pb2
from ortools.constraint_solver import pywrapcp
import ortools

from sklearn.cluster import KMeans # Fallback, not primary anymore
from sklearn.preprocessing import StandardScaler
try:
    from k_means_constrained import KMeansConstrained # Fallback
    HAS_K_MEANS_CONSTRAINED = True
except ImportError:
    HAS_K_MEANS_CONSTRAINED = False

import sys
import traceback
import subprocess
import platform

# --- Helper Function: Haversine Distance ---
def haversine(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    try:
        f_lat1, f_lon1, f_lat2, f_lon2 = float(lat1), float(lon1), float(lat2), float(lon2)
        lon1_r, lat1_r, lon2_r, lat2_r = map(math.radians, [f_lon1, f_lat1, f_lon2, f_lat2])
    except (TypeError, ValueError, AttributeError): return np.nan
    dlon = lon2_r - lon1_r
    dlat = lat2_r - lat1_r
    a = math.sin(dlat / 2)**2 + math.cos(lat1_r) * math.cos(lat2_r) * math.sin(dlon / 2)**2
    if a < 0: a = 0.0
    elif a > 1: a = 1.0
    c = 2 * math.asin(math.sqrt(a))
    r = 6371
    return c * r

# --- Main Application Class ---
class RouteOptimizationApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Route Optimizer By Waild El Tayeh v3.0 (Global VRP Assignment)")
        self.root.geometry("850x750")
        self.outlets_data: Optional[pd.DataFrame] = None
        # self.routes format: {rep_id (0-indexed integer): [day1_df, day2_df,... for A/C/B/D planning]}
        self.routes: Dict[int, List[pd.DataFrame]] = {}
        self.schedule: Dict[str, List[Dict[str, Any]]] = {} # Keyed by "Rep N" string
        self.status_var = tk.StringVar(value="Initializing...")
        self.progress_var = tk.DoubleVar(value=0)
        self.processing = False
        self.queue = Queue()
        self.params: Dict[str, Any] = {}
        self.last_export_dir = ""

        self.STATUS_MAP = {}
        self.ROUTING_SUCCESS_CODE = 1
        self.ROUTING_FAIL_TIMEOUT_CODE = 3
        self.estimated_reps_var = tk.StringVar(value="Est. Reps: N/A")

        self.data_summary_title_var = tk.StringVar(value="")
        self.data_summary_total_var = tk.StringVar(value="Total Outlets: N/A")
        self.data_summary_vf2_var = tk.StringVar(value="VF=2: N/A")
        self.data_summary_vf4_var = tk.StringVar(value="VF=4: N/A")
        self.data_summary_footer_var = tk.StringVar(value="")

        self.create_widgets()
        self._initialize_status_map()
        self.layout_widgets()
        self.setup_queue_checker()
        if not HAS_K_MEANS_CONSTRAINED:
            self.log_message("INFO: 'k-means-constrained' not primary. Fallback K-Means if Global VRP fails catastrophically.")
        self._reset_data_summary_vars()
        self.status_var.set("Ready")

    def _initialize_status_map(self):
        self.log_message("Initializing OR-Tools Status Map...")
        self.STATUS_MAP = {
            0: 'ROUTING_NOT_SOLVED', 1: 'ROUTING_SUCCESS', 2: 'ROUTING_FAIL',
            3: 'ROUTING_FAIL_TIMEOUT', 4: 'ROUTING_INVALID',
        }
        try:
            self.ROUTING_SUCCESS_CODE = pywrapcp.RoutingModel.ROUTING_SUCCESS
            self.ROUTING_FAIL_TIMEOUT_CODE = pywrapcp.RoutingModel.ROUTING_FAIL_TIMEOUT
        except AttributeError: self.log_message("Warning: OR-Tools status codes from pywrapcp missing.")
        self.log_message(f"Final OR-Tools Status Map: {self.STATUS_MAP}")

    def create_widgets(self):
        self.param_frame = ttk.LabelFrame(self.root, text="Parameters", padding="10")
        self.min_outlets_var = tk.StringVar(value="8")
        self.max_outlets_var = tk.StringVar(value="12")
        self.working_days_var = tk.StringVar(value="5")
        self.solver_time_limit_var = tk.StringVar(value="60") # Increased default for global VRP

        ttk.Label(self.param_frame, text="Min outlets per day:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.min_outlets_entry = ttk.Entry(self.param_frame, textvariable=self.min_outlets_var, width=10)
        self.min_outlets_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(self.param_frame, text="Max outlets per day:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.max_outlets_entry = ttk.Entry(self.param_frame, textvariable=self.max_outlets_var, width=10)
        self.max_outlets_entry.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(self.param_frame, text="Working days/week:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.working_days_entry = ttk.Entry(self.param_frame, textvariable=self.working_days_var, width=10)
        self.working_days_entry.grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(self.param_frame, text="Solver Time (Global Assign, s):").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.solver_time_limit_entry = ttk.Entry(self.param_frame, textvariable=self.solver_time_limit_var, width=10)
        self.solver_time_limit_entry.grid(row=3, column=1, padx=5, pady=5)
        
        self.daily_solver_time_limit_var = tk.StringVar(value="15") # For per-rep daily planning
        ttk.Label(self.param_frame, text="Solver Time (Daily Plan, s):").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        self.daily_solver_time_limit_entry = ttk.Entry(self.param_frame, textvariable=self.daily_solver_time_limit_var, width=10)
        self.daily_solver_time_limit_entry.grid(row=4, column=1, padx=5, pady=5)


        self.estimated_reps_label = ttk.Label(self.param_frame, textvariable=self.estimated_reps_var, font=("Arial",10,"bold"), background="yellow", padding=5)
        self.estimated_reps_label.grid(row=0, column=2, rowspan=2, padx=20, pady=5, sticky="ew")

        self.data_summary_info_frame = tk.Frame(self.param_frame, background="yellow", bd=1, relief="sunken")
        self.data_summary_info_frame.grid(row=2, column=2, rowspan=3, padx=20, pady=(0,5), sticky="nsew") # Adjusted rowspan
        self.data_summary_info_frame.columnconfigure(0, weight=1)
        ttk.Label(self.data_summary_info_frame, textvariable=self.data_summary_title_var, font=("Arial",9,"italic"), background="yellow", anchor="center").pack(pady=(5,2),fill=tk.X,padx=5)
        ttk.Label(self.data_summary_info_frame, textvariable=self.data_summary_total_var, font=("Arial",9), background="yellow").pack(anchor="w",padx=10,pady=1)
        ttk.Label(self.data_summary_info_frame, textvariable=self.data_summary_vf2_var, font=("Arial",9), background="yellow").pack(anchor="w",padx=10,pady=1)
        ttk.Label(self.data_summary_info_frame, textvariable=self.data_summary_vf4_var, font=("Arial",9), background="yellow").pack(anchor="w",padx=10,pady=1)
        ttk.Label(self.data_summary_info_frame, textvariable=self.data_summary_footer_var, font=("Arial",9,"italic"), background="yellow", anchor="center").pack(pady=(2,5),fill=tk.X,padx=5)

        self.min_outlets_var.trace_add("write", self.validate_inputs)
        self.max_outlets_var.trace_add("write", self.validate_inputs)
        self.working_days_var.trace_add("write", self.validate_inputs)
        self.solver_time_limit_var.trace_add("write", self.validate_inputs)
        self.daily_solver_time_limit_var.trace_add("write", self.validate_inputs)


        self.button_frame = ttk.Frame(self.root, padding="10")
        self.load_button = ttk.Button(self.button_frame, text="Load Data", command=self.load_data, state='disabled')
        self.optimize_button = ttk.Button(self.button_frame, text="Optimize Routes", command=self.start_optimization, state='disabled')
        self.export_button = ttk.Button(self.button_frame, text="Export Results", command=self.start_export, state='disabled')

        self.progress_frame = ttk.Frame(self.root, padding="5")
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var, maximum=100, mode='determinate', length=400)
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor='w', padding=(5,2))
        self.results_frame = ttk.LabelFrame(self.root, text="Log", padding="10")
        self.results_text = tk.Text(self.results_frame, height=15, width=90, wrap=tk.WORD, state='disabled', font=("Courier New",9))
        self.scrollbar = ttk.Scrollbar(self.results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=self.scrollbar.set)
        self.validate_inputs()

    def _reset_data_summary_vars(self):
        self.data_summary_title_var.set("")
        self.data_summary_total_var.set("Total Outlets: N/A")
        self.data_summary_vf2_var.set("VF=2: N/A")
        self.data_summary_vf4_var.set("VF=4: N/A")
        self.data_summary_footer_var.set("")

    def layout_widgets(self):
        self.param_frame.pack(fill=tk.X, padx=10, pady=5); self.param_frame.columnconfigure(2, weight=1)
        self.button_frame.pack(fill=tk.X, padx=10, pady=5)
        self.load_button.pack(side=tk.LEFT, padx=5)
        self.optimize_button.pack(side=tk.LEFT, padx=5)
        self.export_button.pack(side=tk.LEFT, padx=5)
        self.progress_frame.pack(fill=tk.X, padx=10, pady=5); self.progress_bar.pack(fill=tk.X)
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.results_text.pack(side=tk.LEFT,fill=tk.BOTH,expand=True,padx=(0,5)); self.scrollbar.pack(side=tk.RIGHT,fill=tk.Y)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=0, pady=0)

    def validate_inputs(self, *args):
        valid_min, valid_max, valid_days, valid_global_time, valid_daily_time = False, False, False, False, False
        min_val, max_val = 0,0
        try:
            try: min_val = int(self.min_outlets_var.get()); valid_min = min_val > 0
            except: pass
            self.max_outlets_entry.config(state='normal' if valid_min else 'disabled')

            if valid_min:
                try: max_val = int(self.max_outlets_var.get()); valid_max = max_val >= min_val
                except: pass
            self.working_days_entry.config(state='normal' if valid_max else 'disabled')

            if valid_max:
                try: valid_days = 0 < int(self.working_days_var.get()) <= 7
                except: pass
            self.solver_time_limit_entry.config(state='normal' if valid_days else 'disabled')

            if valid_days:
                try: valid_global_time = int(self.solver_time_limit_var.get()) > 0
                except: pass
            self.daily_solver_time_limit_entry.config(state='normal' if valid_global_time else 'disabled')
            
            if valid_global_time:
                try: valid_daily_time = int(self.daily_solver_time_limit_var.get()) > 0
                except: pass


            all_params_valid = valid_min and valid_max and valid_days and valid_global_time and valid_daily_time
            new_load_state = 'normal' if all_params_valid else 'disabled'
            if hasattr(self, 'load_button') and self.load_button.winfo_exists() and self.load_button.cget('state') != new_load_state:
                self.load_button.config(state=new_load_state)
            if not all_params_valid and not self.processing:
                if hasattr(self, 'optimize_button'): self.optimize_button.config(state='disabled')
                if hasattr(self, 'export_button'): self.export_button.config(state='disabled')
        except Exception as e: self.log_message(f"Warning: Input validation error: {e}")

    def load_data(self):
        """Loads data from CSV or Excel, validates, cleans, and updates GUI state."""
        try:
            filename = filedialog.askopenfilename(
                title="Select Outlet Data File", filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv")]
            )
            if not filename: self.log_message("Data loading cancelled."); self._reset_data_summary_vars(); return

            self.queue.put(('status', "Loading file...")); self.queue.put(('progress', 10))
            self.log_message(f"Attempting to load: {filename}"); self._reset_data_summary_vars()

            read_opts = {'dtype': {'latitude':str, 'longitude':str, 'vf':str, 'outletname':str}}
            if filename.lower().endswith('.xlsx'): df = pd.read_excel(filename, engine='openpyxl', **read_opts)
            elif filename.lower().endswith('.csv'): df = pd.read_csv(filename, sep=None, engine='python', **read_opts)
            else: raise ValueError("Unsupported file type.")
            if df.empty: raise ValueError("File is empty.")

            df.columns = df.columns.str.lower().str.strip().str.replace(' ', '_')
            self.outlets_data = df; self.log_message(f"Initial rows: {len(df)}")
            self.validate_data_columns()

            # Data Cleaning (Simplified for brevity, keep thorough checks from before)
            for col in ['latitude','longitude','vf']: self.outlets_data[col] = pd.to_numeric(self.outlets_data[col], errors='coerce')
            self.outlets_data.dropna(subset=['latitude','longitude','vf'], inplace=True)
            self.outlets_data['vf'] = self.outlets_data['vf'].astype(int)
            self.outlets_data = self.outlets_data[self.outlets_data['vf'].isin([2,4])]
            self.outlets_data = self.outlets_data[(self.outlets_data['latitude'].between(-90,90)) & (self.outlets_data['longitude'].between(-180,180))]
            self.outlets_data['outletname'] = self.outlets_data['outletname'].astype(str).str.strip()
            self.outlets_data = self.outlets_data[self.outlets_data['outletname'] != '']
            self.outlets_data.drop_duplicates(subset=[col.lower().strip().replace(' ','_') for col in ['OutletName','Latitude','Longitude']], keep='first', inplace=True) # More robust duplicate check
            if self.outlets_data.empty: raise ValueError("No valid data after cleaning.")

            # Workload Demand for global VRP (visits per cycle)
            self.outlets_data['workload_demand'] = self.outlets_data['vf'].apply(lambda x: 2 if x == 4 else 1)

            total, vf2, vf4 = len(self.outlets_data), (self.outlets_data['vf']==2).sum(), (self.outlets_data['vf']==4).sum()
            self.log_message(f"\n--- Data Analysis (Post-Cleaning) ---\nTotal: {total}, VF2: {vf2}, VF4: {vf4}\n" + "-"*45)
            self.data_summary_title_var.set("--- Data Analysis Complete ---")
            self.data_summary_total_var.set(f"Total Valid Unique Outlets: {total}")
            self.data_summary_vf2_var.set(f"  - VF=2: {vf2}")
            self.data_summary_vf4_var.set(f"  - VF=4: {vf4}")
            self.data_summary_footer_var.set("-" * (len("--- Data Analysis Complete ---") + 2))

            self.routes = {}; self.schedule = {}
            self.enable_buttons()
            if hasattr(self, 'export_button'): self.export_button.config(state='disabled')
            self.log_message("Data loaded successfully."); self.queue.put(('status',"Data Loaded.")); self.queue.put(('progress',100))
        except Exception as e:
            self.log_message(f"ERROR loading data: {e}"); self.queue.put(('error', f"Data Load Error: {e}"))
            self.outlets_data = None; self.routes={}; self.schedule={}; self._reset_data_summary_vars()
            self.queue.put(('status',"Data loading failed.")); self.queue.put(('progress',0)); self.disable_buttons(); self.validate_inputs()

    def validate_data_columns(self):
        if self.outlets_data is None: raise ValueError("No data loaded.")
        required = {'outletname', 'latitude', 'longitude', 'vf'}
        if not required.issubset(self.outlets_data.columns):
            raise ValueError(f"Missing cols: {required - set(self.outlets_data.columns)}")
        self.log_message("Required columns found.")

    def start_optimization(self):
        if self.processing: messagebox.showwarning("Busy","Busy",parent=self.root); return
        if self.outlets_data is None or self.outlets_data.empty: messagebox.showerror("Error","No data.",parent=self.root); return
        if not self.validate_parameters(): messagebox.showerror("Error","Invalid params.",parent=self.root); return

        self.processing = True; self.disable_buttons()
        self.log_message("\n" + "="*15 + " Starting Global Route Optimization " + "="*15)
        self.progress_var.set(0); self.status_var.set("Initializing global optimization...")
        self.routes={}; self.schedule={}
        threading.Thread(target=self.optimize_routes_task_global_vrp, daemon=True).start()

    def validate_parameters(self) -> bool:
        try:
            min_o = int(self.min_outlets_var.get())
            max_o = int(self.max_outlets_var.get())
            wd = int(self.working_days_var.get())
            gtl = int(self.solver_time_limit_var.get()) # Global time limit
            dtl = int(self.daily_solver_time_limit_var.get()) # Daily time limit

            if not (min_o > 0 and max_o >= min_o and 0 < wd <= 7 and gtl > 0 and dtl > 0):
                raise ValueError("Parameter range error.")
            self.params = {'min_outlets':min_o, 'max_outlets':max_o, 'working_days':wd, 
                           'global_time_limit':gtl, 'daily_time_limit':dtl}
            return True
        except (ValueError, tk.TclError) as e: self.log_message(f"ERROR params: {e}"); self.params={}; return False

    def optimize_routes_task_global_vrp(self):
        """New main optimization task using Global VRP for assignment."""
        try:
            if self.outlets_data is None or self.outlets_data.empty: raise ValueError("No outlet data.")
            if not self.validate_parameters(): raise ValueError("Invalid parameters.")
            self.log_message(f"Using Global VRP approach. Params: {self.params}")

            # 1. Calculate required reps and target workload per rep per cycle
            self.queue.put(('status', "Calculating reps & workload...")); self.queue.put(('progress', 5))
            required_reps = self.calculate_required_reps()
            self.queue.put(('update_est_reps', required_reps if required_reps is not None else "Error"))
            if required_reps is None or required_reps <= 0:
                self.log_message("Optimization stopped: 0 or invalid reps required.")
                self.queue.put(('status', "Complete: No reps required.")); self.queue.put(('progress',100)); self.queue.put(('complete','optimization')); return

            avg_outlets_per_day = (self.params['min_outlets'] + self.params['max_outlets']) / 2.0
            target_workload_per_rep_cycle = math.ceil(avg_outlets_per_day * self.params['working_days'] * 2.0) # 2-week cycle
            self.log_message(f"Target workload per rep per 2-week cycle: {target_workload_per_rep_cycle} visits")

            # 2. Global VRP for outlet assignment
            self.queue.put(('status', "Assigning outlets to reps (Global VRP)...")); self.queue.put(('progress', 10))
            assigned_outlets_per_rep = self.assign_outlets_to_reps_vrp(
                self.outlets_data, required_reps, target_workload_per_rep_cycle
            )

            if not assigned_outlets_per_rep:
                raise ValueError("Global VRP for outlet assignment failed or returned no assignments.")
            
            self.log_message(f"Global VRP assigned outlets to {len(assigned_outlets_per_rep)} reps.")
            for rep_idx, outlets_df_for_rep in assigned_outlets_per_rep.items():
                self.log_message(f"  Rep {rep_idx + 1} assigned {len(outlets_df_for_rep)} outlets. Workload: {outlets_df_for_rep['workload_demand'].sum()}")


            # 3. Per-Rep Daily Route Planning
            self.queue.put(('status', "Planning daily routes per rep...")); self.queue.put(('progress', 50))
            self.routes = {} # rep_idx (int) -> list of daily DFs
            num_assigned_reps = len(assigned_outlets_per_rep)
            processed_reps_daily = 0

            for rep_idx, rep_assigned_data in assigned_outlets_per_rep.items():
                current_progress_daily = 50 + int(40 * (processed_reps_daily / max(1, num_assigned_reps)))
                self.queue.put(('progress', current_progress_daily))
                self.queue.put(('status', f"Daily routes for Rep {rep_idx+1}/{num_assigned_reps}..."))

                self.log_message(f"\n--- Planning daily routes for Rep {rep_idx + 1} ---")
                if rep_assigned_data.empty:
                    self.log_message("  No outlets assigned to this rep. Skipping daily planning.")
                    self.routes[rep_idx] = []
                    processed_reps_daily +=1
                    continue

                vf4_outlets = rep_assigned_data[rep_assigned_data['vf'] == 4]
                vf2_outlets = rep_assigned_data[rep_assigned_data['vf'] == 2]

                if not vf2_outlets.empty:
                    vf2_group_A, vf2_group_B = self.split_vf2_outlets_spatially(vf2_outlets)
                else:
                    vf2_group_A, vf2_group_B = pd.DataFrame(columns=rep_assigned_data.columns), pd.DataFrame(columns=rep_assigned_data.columns)

                outlets_AC = pd.concat([vf4_outlets, vf2_group_A], ignore_index=True) if not (vf4_outlets.empty and vf2_group_A.empty) else pd.DataFrame()
                outlets_BD = pd.concat([vf4_outlets, vf2_group_B], ignore_index=True) if not (vf4_outlets.empty and vf2_group_B.empty) else pd.DataFrame()

                # Use daily_time_limit for these smaller VRPs
                original_time_limit = self.params['time_limit'] # Store global
                self.params['time_limit'] = self.params.get('daily_time_limit', 15) # Set to daily

                routes_AC = [] if outlets_AC.empty else self.plan_days_for_outlets(outlets_AC, rep_idx, "Weeks A/C", self.params['working_days'])
                routes_BD = [] if outlets_BD.empty else self.plan_days_for_outlets(outlets_BD, rep_idx, "Weeks B/D", self.params['working_days'])
                
                self.params['time_limit'] = original_time_limit # Restore global for next potential top-level run

                if routes_AC is not None and routes_BD is not None:
                    self.routes[rep_idx] = routes_AC + routes_BD
                else:
                    self.log_message(f"ERROR: Daily route planning failed for Rep {rep_idx+1}. Assigning empty routes.")
                    self.routes[rep_idx] = []
                processed_reps_daily += 1

            # 4. Create Schedule
            self.queue.put(('status', "Creating 4-week schedule...")); self.queue.put(('progress', 90))
            self.create_schedule() # Uses self.routes (keyed by int rep_idx)

            if self.schedule and any(bool(e) for e in self.schedule.values()):
                self.queue.put(('status', "Optimization successful. Ready to Export."))
            else:
                self.queue.put(('warning', "No schedule data generated (daily planning issue?)."))
                self.queue.put(('status', "Optimization complete (No schedule generated)."))

            self.log_message("="*15 + " Global Optimization Process Complete " + "="*15)
            self.queue.put(('progress', 100))

        except Exception as e:
            self.log_message(f"ERROR during global optimization: {e}\n{traceback.format_exc()}")
            self.queue.put(('error', f"Global Opt. failed: {e}"))
            self.queue.put(('status', "Optimization failed. Check log."))
            self.queue.put(('progress',0))
        finally:
            self.queue.put(('complete', 'optimization'))


    def assign_outlets_to_reps_vrp(self, outlets_df_full: pd.DataFrame, num_reps: int, target_workload_per_rep: int) -> Optional[Dict[int, pd.DataFrame]]:
        """
        Assigns all outlets to representatives using a single VRP run.
        - Outlets have "demands" based on their VF (2 for VF4, 1 for VF2 over a cycle).
        - Reps ("vehicles") have a "capacity" based on target_workload_per_rep.
        - Objective: Minimize travel for assignment, respect capacities, assign all outlets.
        Returns a dictionary: {rep_index (0 to num_reps-1): DataFrame_of_assigned_outlets}
        """
        self.log_message(f"\n--- Global VRP for Outlet Assignment to {num_reps} Reps ---")
        self.log_message(f"Target workload/rep: {target_workload_per_rep} visits per cycle.")

        if outlets_df_full.empty: self.log_message("ERROR: No outlets for global VRP."); return None
        if num_reps <= 0: self.log_message("ERROR: No reps for global VRP."); return None
        if 'workload_demand' not in outlets_df_full.columns:
            self.log_message("ERROR: 'workload_demand' column missing."); return None

        data = outlets_df_full.copy().reset_index(drop=True) # Ensure 0-based indexing for VRP mapping
        num_locations_vrp = len(data) + 1 # +1 for a single central depot

        # Depot: Centroid of all outlets (can be improved, e.g., actual office)
        depot_coords = data[['latitude','longitude']].mean().values
        locations_vrp = np.vstack([depot_coords, data[['latitude','longitude']].values])

        # Distance Matrix
        dist_matrix = np.zeros((num_locations_vrp, num_locations_vrp), dtype=np.int64)
        for i in range(num_locations_vrp):
            for j in range(i, num_locations_vrp):
                d = 0
                if i != j:
                    val = haversine(locations_vrp[i,0], locations_vrp[i,1], locations_vrp[j,0], locations_vrp[j,1])
                    d = int(val * 1000) if pd.notna(val) else ********* # Meters or large penalty
                dist_matrix[i,j] = dist_matrix[j,i] = d
        
        try:
            manager = pywrapcp.RoutingIndexManager(num_locations_vrp, num_reps, 0) # Depot at index 0
            routing = pywrapcp.RoutingModel(manager)
        except Exception as e:
            self.log_message(f"ERROR: Global VRP manager/model init failed: {e}"); return None

        # Distance callback
        def distance_callback_global(from_index, to_index):
            from_node = manager.IndexToNode(from_index)
            to_node = manager.IndexToNode(to_index)
            return dist_matrix[from_node, to_node]
        transit_callback_index_global = routing.RegisterTransitCallback(distance_callback_global)
        routing.SetArcCostEvaluatorOfAllVehicles(transit_callback_index_global)

        # Workload demand callback (demands[0] for depot is 0)
        demands_global = [0] + data['workload_demand'].tolist()
        def workload_demand_callback(from_index):
            return demands_global[manager.IndexToNode(from_index)]
        demand_callback_index_global = routing.RegisterUnaryTransitCallback(workload_demand_callback)

        # Workload capacity dimension for each rep
        # Allow some slack: min slightly less than target, max slightly more
        # This helps if total workload isn't perfectly divisible by num_reps
        min_workload_cap = math.floor(target_workload_per_rep * 0.85) # e.g. 85% of target
        max_workload_cap = math.ceil(target_workload_per_rep * 1.15)  # e.g. 115% of target
        vehicle_capacities_global = [max_workload_cap] * num_reps
        
        self.log_message(f"Global VRP: Min workload/rep: {min_workload_cap}, Max workload/rep: {max_workload_cap}")

        routing.AddDimensionWithVehicleCapacity(
            name='WorkloadCapacity',
            evaluator_index=demand_callback_index_global,
            slack_max=0, # No slack on workload accumulation per step
            vehicle_capacities=vehicle_capacities_global,
            fix_start_cumul_to_zero=True
        )
        workload_dim = routing.GetDimensionOrDie('WorkloadCapacity')

        # Soft constraint to encourage minimum workload
        # Also, ensure all outlets are visited by adding a large penalty for unvisited ones.
        penalty_unvisited = 1000000 # Large penalty
        for node_idx_data in range(len(data)): # 0 to len(data)-1
            # VRP node index is data_idx + 1 (depot is 0)
            routing.AddDisjunction([manager.NodeToIndex(node_idx_data + 1)], penalty_unvisited)
        
        # Soft lower bound for workload per vehicle (rep)
        # To make this a soft constraint (solver tries to achieve it)
        for i in range(num_reps):
             workload_dim.SetCumulVarSoftLowerBound(routing.End(i), min_workload_cap, int(penalty_unvisited / 10)) # penalty smaller than unvisited

        search_parameters = pywrapcp.DefaultRoutingSearchParameters()
        search_parameters.first_solution_strategy = routing_enums_pb2.FirstSolutionStrategy.PARALLEL_CHEAPEST_INSERTION
        search_parameters.local_search_metaheuristic = routing_enums_pb2.LocalSearchMetaheuristic.GUIDED_LOCAL_SEARCH
        search_parameters.time_limit.FromSeconds(self.params.get('global_time_limit', 60))
        #search_parameters.log_search = True

        self.log_message(f"Solving Global VRP for assignments (Time Limit: {self.params.get('global_time_limit', 60)}s)...")
        solution = routing.SolveWithParameters(search_parameters)
        status_code = routing.status()
        status_name = self.STATUS_MAP.get(status_code, f"UNKNOWN_{status_code}")

        assigned_outlets_map: Dict[int, pd.DataFrame] = {i: [] for i in range(num_reps)} # store list of rows first
        
        if solution and (status_code == self.ROUTING_SUCCESS_CODE or status_code == self.ROUTING_FAIL_TIMEOUT_CODE):
            self.log_message(f"Global VRP solution found (Status: {status_name})")
            if status_code == self.ROUTING_FAIL_TIMEOUT_CODE:
                self.log_message("WARNING: Global VRP timed out. Solution may be suboptimal for assignments.")
                self.queue.put(('warning', "Global assignment VRP timed out. Assignments might be suboptimal."))

            total_assigned_outlets = 0
            for vehicle_id in range(num_reps):
                index = routing.Start(vehicle_id)
                route_actual_workload = 0
                while not routing.IsEnd(index):
                    node_index_vrp = manager.IndexToNode(index)
                    route_actual_workload += workload_dim.CumulVar(index).Value() if index != routing.Start(vehicle_id) else 0 # Crude workload sum for log
                    if node_index_vrp > 0: # Not the depot
                        data_idx = node_index_vrp - 1 # Map back to original data DataFrame index
                        assigned_outlets_map[vehicle_id].append(data.iloc[data_idx])
                        total_assigned_outlets +=1
                    index = solution.Value(routing.NextVar(index))
                final_workload = workload_dim.CumulVar(routing.End(vehicle_id)).Value()
                self.log_message(f"  Rep {vehicle_id + 1} (Global VRP) assigned {len(assigned_outlets_map[vehicle_id])} outlets. Route Workload: {final_workload}")

            # Convert lists of rows to DataFrames
            final_assignment_dfs: Dict[int, pd.DataFrame] = {}
            for rep_id, rows_list in assigned_outlets_map.items():
                if rows_list:
                    final_assignment_dfs[rep_id] = pd.DataFrame(rows_list)
                else:
                    final_assignment_dfs[rep_id] = pd.DataFrame(columns=data.columns) # Empty DF if no assignments

            # Check if all outlets were assigned
            if total_assigned_outlets < len(data):
                self.log_message(f"WARNING: Global VRP assigned {total_assigned_outlets} outlets, but {len(data)} were available. Some outlets are unassigned.")
                self.queue.put(('warning', f"{len(data) - total_assigned_outlets} outlets UNASSIGNED by Global VRP."))
                # Potentially add unassigned outlets to the nearest rep or a fallback rep later if this becomes an issue.
                # For now, we proceed with what was assigned.
            
            return final_assignment_dfs
        else:
            self.log_message(f"ERROR: Global VRP for assignment failed or no solution. Status: {status_name} (Code: {status_code})")
            # Attempt K-Means as a fallback
            self.log_message("FALLBACK: Global VRP failed, attempting K-Means clustering for rep assignment...")
            self.queue.put(('warning', "Global VRP assignment failed. Falling back to K-Means clustering (may be suboptimal)."))
            self.cluster_outlets_balanced_kmeans(num_reps) # This sets self.outlets_data['cluster']
            
            fallback_assignments: Dict[int, pd.DataFrame] = {}
            if 'cluster' in self.outlets_data.columns:
                for i in range(num_reps):
                    cluster_outlets = self.outlets_data[self.outlets_data['cluster'] == i]
                    fallback_assignments[i] = cluster_outlets
                self.log_message(f"Fallback K-Means assigned outlets to {len(fallback_assignments)} clusters/reps.")
                return fallback_assignments
            else:
                self.log_message("ERROR: Fallback K-Means also failed to produce assignments.")
                return None
    
    # K-Means and _balance_clusters_heuristic are kept as potential fallbacks or for other uses
    def _balance_clusters_heuristic(self, coords_df, labels, num_clusters, max_iterations=20, tolerance_factor=0.10):
        """Heuristic to balance K-Means cluster sizes."""
        # (Code from previous version, ensure it's up-to-date with your last good version)
        self.log_message("Attempting heuristic balancing of K-Means clusters...")
        current_labels = labels.copy()
        if num_clusters == 0: self.log_message("Heuristic: num_clusters is 0."); return current_labels
        base_target_size = len(coords_df) / num_clusters
        min_allowed, max_allowed = math.floor(base_target_size*(1-tolerance_factor)), math.ceil(base_target_size*(1+tolerance_factor))

        for iteration in range(max_iterations):
            unique, counts = np.unique(current_labels, return_counts=True); cluster_sizes = dict(zip(unique,counts))
            if all(min_allowed <= s <= max_allowed for s in counts): self.log_message(f"Heuristic: Balanced iter {iteration}."); break
            if not cluster_sizes: continue
            sorted_clusters = sorted(cluster_sizes.items(),key=lambda i:i[1]);
            if not sorted_clusters: break
            s_id,s_sz = sorted_clusters[0]; l_id,l_sz = sorted_clusters[-1]
            if s_sz >= min_allowed and l_sz <= max_allowed: break
            if l_sz <= min_allowed or s_sz >= max_allowed : break
            
            largest_indices = np.where(current_labels == l_id)[0]
            if not largest_indices.size: break
            smallest_indices = np.where(current_labels == s_id)[0]
            if not smallest_indices.size:
                current_labels[largest_indices[0]] = s_id; continue 

            s_centroid = coords_df.iloc[smallest_indices][['latitude','longitude']].mean().values
            best_move_idx = -1; min_dist = float('inf')
            for outlet_df_idx in largest_indices:
                coord = coords_df.iloc[outlet_df_idx][['latitude','longitude']].values
                dist = np.linalg.norm(coord - s_centroid)
                if dist < min_dist: min_dist = dist; best_move_idx = outlet_df_idx
            if best_move_idx != -1: current_labels[best_move_idx] = s_id
            else: break
        else: self.log_message("Heuristic: Max iterations.")
        return current_labels

    def cluster_outlets_balanced_kmeans(self, num_clusters: int):
        """Fallback K-Means clustering."""
        # (Code from previous version, ensure it's up-to-date)
        if self.outlets_data is None or self.outlets_data.empty: self.log_message("KMeans: No data."); return
        if num_clusters <= 0: self.outlets_data['cluster'] = 0; return
        
        coords_df = self.outlets_data[['latitude', 'longitude']]; coords = coords_df.values; total_outlets = len(coords_df)
        if num_clusters >= total_outlets:
            if total_outlets > 0: self.outlets_data.loc[:, 'cluster'] = np.arange(total_outlets)
            return
        
        target_min = max(1, total_outlets//num_clusters); target_max = math.ceil(total_outlets/num_clusters) + math.ceil(num_clusters*0.1)
        self.log_message(f"KMeans Target sizes: min ~{target_min}, max ~{target_max}")

        try:
            if HAS_K_MEANS_CONSTRAINED:
                clf = KMeansConstrained(n_clusters=num_clusters, size_min=target_min, size_max=target_max, random_state=42, n_init=10)
                self.outlets_data.loc[:, 'cluster'] = clf.fit_predict(coords)
                # (Add checks for imbalance and potential fallback to standard KMeans if needed)
            else:
                kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init='auto')
                raw_labels = kmeans.fit_predict(coords)
                self.outlets_data.loc[:, 'cluster'] = self._balance_clusters_heuristic(coords_df, raw_labels, num_clusters)
            self.log_message("K-Means clustering fallback applied.")
        except Exception as e:
            self.log_message(f"CRITICAL K-Means Error: {e}. Assigning all to cluster 0.")
            self.outlets_data.loc[:, 'cluster'] = 0


    def calculate_required_reps(self) -> Optional[int]:
        """Calculates estimated representatives needed."""
        # (Code from previous version)
        try:
            if self.outlets_data is None or self.outlets_data.empty: return None
            p = self.params; min_o,max_o,wd = p.get('min_outlets'),p.get('max_outlets'),p.get('working_days')
            if not all(isinstance(v,int) and v>0 for v in [min_o,max_o,wd] if v) or (min_o and max_o and max_o < min_o): return None
            
            total_o = len(self.outlets_data); vf4 = (self.outlets_data['vf']==4).sum(); vf2 = total_o - vf4
            visits_2w = (vf4*2)+vf2
            avg_o_day = (min_o+max_o)/2.0; visits_rep_2w = avg_o_day*wd*2.0
            if visits_rep_2w <=0: return None
            req_reps = math.ceil(visits_2w / visits_rep_2w)
            self.log_message(f"Est. Reps: {req_reps} (based on {total_o} outlets, avg {avg_o_day:.1f}/day).")
            return int(req_reps)
        except Exception as e: self.log_message(f"Rep calc error: {e}"); return None


    def cartesian_to_polar(self, coords: np.ndarray, centroid: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        delta = np.asarray(coords) - np.asarray(centroid)
        radius = np.linalg.norm(delta, axis=1)
        angle_normalized = (np.degrees(np.arctan2(delta[:, 0], delta[:, 1])) + 360) % 360
        return angle_normalized, radius

    def split_vf2_outlets_spatially(self, vf2_outlets_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Splits VF2 outlets spatially into two roughly equal groups."""
        # (Code from previous version)
        if vf2_outlets_df.empty: return pd.DataFrame(columns=vf2_outlets_df.columns), pd.DataFrame(columns=vf2_outlets_df.columns)
        if len(vf2_outlets_df) <= 1: return vf2_outlets_df.copy(), pd.DataFrame(columns=vf2_outlets_df.columns)
        
        coords_df = vf2_outlets_df[['latitude','longitude']].copy()
        for col in ['latitude','longitude']: coords_df[col] = pd.to_numeric(coords_df[col],errors='coerce')
        
        original_indices = vf2_outlets_df.index
        coords_df.dropna(inplace=True); valid_indices = coords_df.index
        if len(coords_df) <=1: return vf2_outlets_df.copy(), pd.DataFrame(columns=vf2_outlets_df.columns)

        centroid = coords_df.mean().values
        angles, _ = self.cartesian_to_polar(coords_df.values, centroid)
        
        vf2_angles = vf2_outlets_df.loc[valid_indices].copy(); vf2_angles['angle'] = angles
        vf2_sorted = vf2_angles.sort_values('angle')
        split_idx = len(vf2_sorted) // 2
        
        gA_valid = vf2_sorted.iloc[:split_idx].drop(columns=['angle'])
        gB_valid = vf2_sorted.iloc[split_idx:].drop(columns=['angle'])
        
        gA_final, gB_final = gA_valid, gB_valid
        invalid_indices = original_indices.difference(valid_indices)
        if not invalid_indices.empty:
            gA_final = pd.concat([gA_valid, vf2_outlets_df.loc[invalid_indices]], ignore_index=False)
        
        return gA_final.reset_index(drop=True), gB_final.reset_index(drop=True)

    def plan_days_for_outlets(self, outlets_df: pd.DataFrame, rep_id_for_log: int, week_type: str, working_days: int) -> Optional[List[pd.DataFrame]]:
        """Uses OR-Tools VRP solver to plan daily routes for a given set of outlets for a specific rep."""
        # This function remains largely the same as your v2.29 but uses self.params['daily_time_limit']
        # The rep_id_for_log is passed for clearer logging.
        log_prefix = f"Rep {rep_id_for_log + 1} (Daily), {week_type}" # Make clear this is daily planning
        # ... (rest of the plan_days_for_outlets logic from your v2.29) ...
        # Important: Ensure it uses self.params['daily_time_limit'] for its solver.
        # Example modification point for time limit:
        # solver_time_limit_seconds = self.params.get('daily_time_limit', 15) # Use daily_time_limit
        # search_parameters.time_limit.FromSeconds(solver_time_limit_seconds)
        
        # --- Re-inserting full plan_days_for_outlets for completeness with time limit adjustment ---
        if outlets_df.empty: self.log_message(f"  {log_prefix}: No outlets."); return []
        required_cols = ['latitude', 'longitude', 'outletname', 'vf']
        if not all(col in outlets_df.columns for col in required_cols):
            self.log_message(f"ERROR ({log_prefix}): Missing cols: {set(required_cols)-set(outlets_df.columns)}."); return None
        
        min_v, max_v = self.params.get('min_outlets',1), self.params.get('max_outlets',1)
        if not (isinstance(max_v,int) and max_v>0): self.log_message(f"ERROR ({log_prefix}): Invalid max outlets {max_v}."); return None
        min_v = max(0, min_v if isinstance(min_v,int) else 0)

        vrp_data = outlets_df[required_cols].copy()
        for col in ['latitude','longitude']: vrp_data[col]=pd.to_numeric(vrp_data[col],errors='coerce')
        init_c=len(vrp_data); vrp_data.dropna(subset=['latitude','longitude'],inplace=True)
        if init_c!=len(vrp_data): self.log_message(f" {log_prefix}: Dropped {init_c-len(vrp_data)} invalid coord rows.")
        if vrp_data.empty: self.log_message(f"ERROR ({log_prefix}): No valid coord outlets for daily plan."); return None
        
        n_outlets=len(vrp_data); vrp_data=vrp_data.reset_index(drop=True)
        days_est = math.ceil(n_outlets/max_v) if max_v>0 else n_outlets
        num_vehicles = max(1,min(days_est,working_days,n_outlets)) if n_outlets>0 else 0
        if num_vehicles==0: return []
        self.log_message(f"  Planning {log_prefix}: {n_outlets} outlets -> Target {num_vehicles} day(s) (Min/Max {min_v}/{max_v}).")

        depot_loc = vrp_data[['latitude','longitude']].mean().values
        vrp_locs = np.vstack([depot_loc, vrp_data[['latitude','longitude']].values]); n_vrp_locs = len(vrp_locs)
        dist_matrix = np.array([[int(haversine(vrp_locs[i,0],vrp_locs[i,1],vrp_locs[j,0],vrp_locs[j,1])*1000) if i!=j and pd.notna(haversine(vrp_locs[i,0],vrp_locs[i,1],vrp_locs[j,0],vrp_locs[j,1])) else (0 if i==j else *********) for j in range(n_vrp_locs)] for i in range(n_vrp_locs)], dtype=np.int64)
        
        manager = pywrapcp.RoutingIndexManager(n_vrp_locs, num_vehicles, 0); routing = pywrapcp.RoutingModel(manager)
        def dist_cb(fi,ti): return dist_matrix[manager.IndexToNode(fi),manager.IndexToNode(ti)]
        transit_idx = routing.RegisterTransitCallback(dist_cb); routing.SetArcCostEvaluatorOfAllVehicles(transit_idx)
        
        vrp_demands=[0]+[1]*n_outlets
        def demand_cb(fi): return vrp_demands[manager.IndexToNode(fi)]
        demand_idx = routing.RegisterUnaryTransitCallback(demand_cb)
        routing.AddDimensionWithVehicleCapacity('Capacity', demand_idx, 0, [max_v]*num_vehicles, True)
        if min_v > 0:
            cap_dim=routing.GetDimensionOrDie('Capacity'); penalty = np.max(dist_matrix)*n_vrp_locs if np.max(dist_matrix) < 9999999 else ********
            for v_id in range(num_vehicles): cap_dim.SetCumulVarSoftLowerBound(routing.End(v_id), min_v, penalty)

        search_params = pywrapcp.DefaultRoutingSearchParameters()
        search_params.first_solution_strategy = routing_enums_pb2.FirstSolutionStrategy.AUTOMATIC
        search_params.local_search_metaheuristic = routing_enums_pb2.LocalSearchMetaheuristic.TABU_SEARCH
        
        # CRITICAL: Use daily_time_limit here
        daily_time_limit = self.params.get('daily_time_limit', 15)
        search_params.time_limit.FromSeconds(daily_time_limit)
        # search_params.log_search = True

        self.queue.put(('status', f"Solving VRP (daily) for {log_prefix}... ({daily_time_limit}s)"))
        solution = routing.SolveWithParameters(search_params)
        status_code = routing.status(); status_name = self.STATUS_MAP.get(status_code,f"UNK_{status_code}")

        if solution and (status_code==self.ROUTING_SUCCESS_CODE or status_code==self.ROUTING_FAIL_TIMEOUT_CODE):
            if status_code!=self.ROUTING_SUCCESS_CODE: self.queue.put(('warning',f"Daily VRP ({log_prefix}) status {status_name}"))
            daily_routes_dfs = []; assigned_indices = set()
            for veh_id in range(num_vehicles):
                idx = routing.Start(veh_id); route_indices=[]
                while not routing.IsEnd(idx):
                    node_idx = manager.IndexToNode(idx)
                    if node_idx!=0: vrp_data_idx = node_idx-1; route_indices.append(vrp_data_idx); assigned_indices.add(vrp_data_idx)
                    idx = solution.Value(routing.NextVar(idx))
                if route_indices:
                    day_df = vrp_data.iloc[route_indices].copy()
                    day_df['Visit Order'] = day_df.index.map({orig_idx:order+1 for order,orig_idx in enumerate(route_indices)})
                    daily_routes_dfs.append(day_df)
            if set(range(n_outlets)) - assigned_indices: self.queue.put(('warning',f"{log_prefix}: {(set(range(n_outlets))-assigned_indices).__len__()} unassigned daily outlets."))
            return daily_routes_dfs
        else: self.log_message(f"ERROR ({log_prefix}): Daily VRP failed. Status {status_name}"); return None

    def create_schedule(self):
        """Constructs 4-week schedule from self.routes (keyed by int rep_idx)."""
        self.log_message("\n--- Creating 4-Week Schedule (using Global VRP assignments) ---")
        self.schedule = {}
        if not isinstance(self.routes, dict) or not self.routes:
            self.log_message("Schedule Creation: No routes from daily planning."); return

        wd = self.params.get('working_days', 5)
        days_of_week_names = list(calendar.day_name)[:wd]

        # self.routes keys are 0-indexed integer rep_idx from global VRP
        for rep_idx_int, combined_routes_for_rep_int_key in self.routes.items():
            rep_name_str = f'Rep {rep_idx_int + 1}' # Create string name "Rep N"

            if not isinstance(combined_routes_for_rep_int_key, list):
                self.log_message(f"Sched Warning ({rep_name_str}): Invalid route data."); continue
            if not combined_routes_for_rep_int_key:
                self.log_message(f"Sched Info ({rep_name_str}): No daily routes planned."); continue

            # Split point logic (same as before, relies on AC routes then BD routes in list)
            # Get assigned outlets for this rep_idx_int to make split decision
            assigned_outlets_for_this_rep = pd.DataFrame() # Placeholder
            # Need to access the output of `assign_outlets_to_reps_vrp` or re-filter from `self.outlets_data` if it stored a 'rep_assignment' column.
            # For simplicity, let's assume the global VRP's assignments could be temporarily stored or re-calculated for this split logic.
            # As `assign_outlets_to_reps_vrp` is complex to re-run, a better way would be to pass its output or ensure `self.outlets_data` is tagged.
            # Current structure means we just split the combined_routes list based on expected number of A/C days
            
            # A simpler split (assuming daily VRPs were called sequentially for AC then BD):
            # Estimate days needed for first half (AC)
            # Heuristic: split in half or use param working_days for first block
            num_ac_routes_expected = wd # Max days for AC
            split_point_idx = min(num_ac_routes_expected, len(combined_routes_for_rep_int_key))
            
            # If you need a more precise split based on vf2 content like before, you'd need rep_assigned_data here.
            # This would require either re-filtering `self.outlets_data` if it got a 'rep_assignment' column
            # or storing `assigned_outlets_per_rep` from the global VRP step more permanently.
            # For now, simpler split:
            self.log_message(f"  Sched Split ({rep_name_str}): Using simpler split point at {split_point_idx} (up to {wd} days for A/C).")

            actual_routes_AC = combined_routes_for_rep_int_key[:split_point_idx]
            actual_routes_BD = combined_routes_for_rep_int_key[split_point_idx:]

            padded_AC = actual_routes_AC + [pd.DataFrame()] * max(0, wd - len(actual_routes_AC))
            padded_BD = actual_routes_BD + [pd.DataFrame()] * max(0, wd - len(actual_routes_BD))

            rep_schedule_entries_list = []
            for week_number in range(1,5):
                is_AC = (week_number % 2 != 0)
                routes_for_week = padded_AC if is_AC else padded_BD
                week_type = "A/C" if is_AC else "B/D"
                for day_idx, day_name in enumerate(days_of_week_names):
                    daily_route_df = routes_for_week[day_idx] if day_idx < len(routes_for_week) else pd.DataFrame()
                    outlets = []; count = 0
                    if isinstance(daily_route_df, pd.DataFrame) and not daily_route_df.empty and 'outletname' in daily_route_df:
                        df_proc = daily_route_df.sort_values(by='Visit Order') if 'Visit Order' in daily_route_df else daily_route_df
                        outlets = df_proc['outletname'].astype(str).tolist(); count = len(df_proc)
                    rep_schedule_entries_list.append({
                        'Week': week_number, 'Day': day_name, 'Week Type': week_type,
                        'Outlets': outlets, 'Count': count, 'RouteDF': daily_route_df
                    })
            if rep_schedule_entries_list:
                self.schedule[rep_name_str] = rep_schedule_entries_list # Store with string key "Rep N"

        if not self.schedule or not any(self.schedule.values()):
            self.log_message("Schedule Creation: No schedules generated (final step).")
        else:
            self.log_message(f"Schedule Creation: Schedules generated for {len(self.schedule)} reps (final step).")


    def start_export(self):
        """Initiates export process."""
        # (Code from previous version)
        if self.processing: messagebox.showwarning("Busy","Busy",parent=self.root); return
        has_data = isinstance(self.schedule,dict) and self.schedule and any(isinstance(e,list) and e and any(isinstance(d,dict) and isinstance(d.get('RouteDF'),pd.DataFrame) and not d['RouteDF'].empty for d in e) for e in self.schedule.values())
        if not has_data: messagebox.showinfo("Export Info","No schedule data.",parent=self.root); return
        
        self.processing = True; self.disable_buttons()
        self.log_message("\n"+"="*15+" Starting Export "+"="*15); self.progress_var.set(0); self.status_var.set("Exporting...")
        threading.Thread(target=self.export_results_task, daemon=True).start()

    def export_results_task(self):
        """Exports results to Excel and HTML maps."""
        # (Code from previous version, ensure it works with self.schedule keyed by "Rep N")
        base_export_dir = ""; export_succeeded = False
        try:
            self.queue.put(('status', "Preparing export...")); self.queue.put(('progress', 5))
            # Re-check data in thread
            if not (isinstance(self.schedule,dict) and self.schedule and any(isinstance(e,list) and e and any(isinstance(d,dict) and isinstance(d.get('RouteDF'),pd.DataFrame) and not d['RouteDF'].empty for d in e) for e in self.schedule.values())):
                self.log_message("Export: No data in thread."); self.queue.put(('error',"No data to export in thread.")); self.queue.put(('complete','export')); return

            timestamp=datetime.now().strftime("%Y%m%d_%H%M%S"); home=os.path.expanduser("~")
            base_export_dir=os.path.join(home, f"RouteOptimizerResults_{timestamp}")
            excel_subdir=os.path.join(base_export_dir,"ExcelSchedules"); maps_subdir=os.path.join(base_export_dir,"HTML_Maps")
            os.makedirs(excel_subdir,exist_ok=True); os.makedirs(maps_subdir,exist_ok=True); self.last_export_dir = base_export_dir
            
            excel_start, excel_range = 10, 40; combined_rows = []
            reps_data = {rn: en for rn,en in self.schedule.items() if isinstance(en,list) and en}
            total_reps = max(1, len(reps_data)); exported_count=0
            day_order_map = {name:i for i, name in enumerate(calendar.day_name)}

            for rep_name, sched_list in reps_data.items(): # rep_name is "Rep N" string
                prog = excel_start + int(excel_range * (exported_count/total_reps))
                self.queue.put(('progress', prog)); self.queue.put(('status',f"Excel: {rep_name}..."))
                rep_rows = []
                sorted_sched = sorted(sched_list, key=lambda x:(x.get('Week',0), day_order_map.get(x.get('Day',''),99)))
                for entry in sorted_sched:
                    rdf = entry.get('RouteDF')
                    if isinstance(rdf, pd.DataFrame) and not rdf.empty:
                        df_exp = rdf.sort_values('Visit Order') if 'Visit Order' in rdf else rdf # Simplified sort
                        for _, orow in df_exp.iterrows():
                            row_d = {'Representative': rep_name, 'Week': entry.get('Week'), 'Day': entry.get('Day'),
                                     'Visit_Order': int(orow.get('Visit Order',0)), 'Outlet_Name': orow.get('outletname'),
                                     'Latitude': orow.get('latitude'), 'Longitude': orow.get('longitude'), 'VF': orow.get('vf')}
                            rep_rows.append(row_d); combined_rows.append(row_d)
                if rep_rows:
                    df_rep = pd.DataFrame(rep_rows); safe_fn = rep_name.replace(" ","_")
                    xl_path = os.path.join(excel_subdir,f"{safe_fn}_schedule.xlsx")
                    try:
                        with pd.ExcelWriter(xl_path,engine='xlsxwriter') as writer:
                            df_rep.to_excel(writer,index=False,sheet_name='Schedule'); ws=writer.sheets['Schedule']
                            for i,col in enumerate(df_rep.columns): ws.set_column(i,i,max(df_rep[col].astype(str).map(len).max() if not df_rep[col].empty else 0, len(col))+2)
                    except Exception as e_xl: self.log_message(f"ERROR Excel {rep_name}:{e_xl}")
                exported_count+=1
            
            if combined_rows:
                df_comb=pd.DataFrame(combined_rows)
                # Correct sort for combined
                df_comb['DayOrder'] = df_comb['Day'].map(day_order_map).fillna(99)
                df_comb = df_comb.sort_values(['Representative','Week','DayOrder','Visit_Order']).drop(columns=['DayOrder'])
                comb_path = os.path.join(base_export_dir, "Combined_Schedule.xlsx")
                with pd.ExcelWriter(comb_path,engine='xlsxwriter') as writer:
                    df_comb.to_excel(writer,index=False,sheet_name='Combined'); ws=writer.sheets['Combined']
                    for i,col in enumerate(df_comb.columns): ws.set_column(i,i,max(df_comb[col].astype(str).map(len).max() if not df_comb[col].empty else 0, len(col))+2)
            
            map_start, map_range = 50,50
            self.queue.put(('progress',map_start)); self.queue.put(('status',"Creating maps..."))
            self.create_and_save_maps(maps_subdir, reps_data, map_start, map_range) # Pass reps_data
            
            export_succeeded = True
        except Exception as e: self.log_message(f"CRITICAL EXPORT ERR: {e}\n{traceback.format_exc()}"); self.queue.put(('error',f"Export Failed: {e}"))
        finally:
            self.queue.put(('progress',100))
            if export_succeeded and base_export_dir and os.path.exists(base_export_dir):
                self.log_message(f"Export OK. Path: {os.path.abspath(base_export_dir)}")
                self.queue.put(('status',"Export complete.")); self.queue.put(('ask_open_folder',os.path.abspath(base_export_dir)))
            else: self.log_message("Export finished with errors or dir issue.")
            self.queue.put(('complete','export'))

    def create_and_save_maps(self, maps_dir: str, schedule_data_for_maps: Dict[str, list], progress_start: int, progress_range: int):
        """Creates Folium maps. schedule_data_for_maps keys are "Rep N" strings."""
        # (Code from previous version, should largely work as keys are already "Rep N")
        self.log_message("\n--- Creating Route Maps ---")
        if not schedule_data_for_maps: self.log_message("Map: No data."); self.queue.put(('progress',progress_start+progress_range)); return
        
        route_colors = ['#a6cee3','#1f78b4','#b2df8a','#33a02c','#fb9a99','#e31a1c','#fdbf6f','#ff7f00'] * 3 # More colors
        map_count=0; total_reps_maps = max(1, len(schedule_data_for_maps)); first_map_path = None

        for rep_name, sched_list in schedule_data_for_maps.items(): # rep_name is already "Rep N"
            prog = progress_start + int(progress_range * (map_count/total_reps_maps))
            self.queue.put(('progress',prog)); self.queue.put(('status', f"Map: {rep_name}..."))
            safe_fn = rep_name.replace(" ","_"); map_path = os.path.join(maps_dir, f"{safe_fn}_W1W2_Routes.html")

            # Filter routes for W1 and W2 for this rep_name (string key)
            routes_w1 = [e['RouteDF'] for e in sched_list if e.get('Week')==1 and isinstance(e.get('RouteDF'),pd.DataFrame) and not e['RouteDF'].empty]
            routes_w2 = [e['RouteDF'] for e in sched_list if e.get('Week')==2 and isinstance(e.get('RouteDF'),pd.DataFrame) and not e['RouteDF'].empty]
            if not routes_w1 and not routes_w2: map_count+=1; continue
            
            # Center map
            all_pts = pd.concat([df[['latitude','longitude']] for df_list in [routes_w1,routes_w2] for df in df_list if not df.empty], ignore_index=True)
            all_pts.dropna(inplace=True) # Make sure coords are valid after concat
            if all_pts.empty : map_center = [24.7, 46.6]; map_bounds = None # Fallback
            else:
                map_center = all_pts.mean().tolist()
                map_bounds = [[all_pts.latitude.min(), all_pts.longitude.min()], [all_pts.latitude.max(), all_pts.longitude.max()]]
                if (map_bounds[1][0]-map_bounds[0][0]<0.01) or (map_bounds[1][1]-map_bounds[0][1]<0.01):
                    map_bounds = [[map_bounds[0][0]-0.005,map_bounds[0][1]-0.005],[map_bounds[1][0]+0.005,map_bounds[1][1]+0.005]]

            f_map = folium.Map(location=map_center, tiles='CartoDB positron')

            def add_week_routes_to_map(week_num, routes_list, week_type, color_offset):
                # ... (inner logic from previous create_and_save_maps)
                if not routes_list: return
                fg_lines = folium.FeatureGroup(name=f"W{week_num} ({week_type}) Routes",show=True).add_to(f_map)
                fg_markers = folium.FeatureGroup(name=f"W{week_num} ({week_type}) Markers",show=True).add_to(f_map)
                for day_idx, day_df in enumerate(routes_list):
                    if day_df.empty: continue
                    day_df_sorted = day_df.sort_values('Visit Order') if 'Visit Order' in day_df else day_df
                    poly_pts = []
                    for _,orow in day_df_sorted.iterrows():
                        try: lat,lon=float(orow['latitude']),float(orow['longitude']); poly_pts.append([lat,lon])
                        except: continue
                        name,vf,vnum=str(orow['outletname']),orow.get('vf',0),int(orow.get('Visit Order',0))
                        popup = f"<b>{name}</b><br>W{week_num}D{day_idx+1} #{vnum}<br>VF:{vf}<br>({lat:.5f},{lon:.5f})"
                        folium.Marker([lat,lon],popup=folium.Popup(popup,max_width=250),tooltip=f"{name}", icon=folium.Icon(color='purple' if vf==4 else 'blue',icon='star' if vf==4 else 'info-sign')).add_to(fg_markers)
                    if len(poly_pts)>1: folium.PolyLine(poly_pts,color=route_colors[(day_idx+color_offset)%len(route_colors)],weight=2.5).add_to(fg_lines)
            
            add_week_routes_to_map(1, routes_w1, "A/C", 0)
            add_week_routes_to_map(2, routes_w2, "B/D", len(routes_w1))
            
            if map_bounds: f_map.fit_bounds(map_bounds)
            folium.LayerControl().add_to(f_map)
            f_map.save(map_path); first_map_path = first_map_path or os.path.abspath(map_path)
            map_count+=1
        
        if first_map_path: self.queue.put(('ask_open_map',first_map_path))
        self.queue.put(('progress',progress_start+progress_range))

    # GUI management: disable_buttons, enable_buttons, log_message, _log_message_gui, show_error/warning, setup_queue_checker, _open_file_or_folder
    # These remain largely the same as your v2.29, ensure parameter name for time limits is updated in enable/disable logic if touched.
    def disable_buttons(self):
        widgets = [getattr(self,w,None) for w in ['load_button','optimize_button','export_button','min_outlets_entry',
                                                  'max_outlets_entry','working_days_entry','solver_time_limit_entry',
                                                  'daily_solver_time_limit_entry']] # Added daily time limit entry
        for widget in widgets:
            if widget and widget.winfo_exists():
                try: widget.config(state='disabled')
                except tk.TclError: pass
    
    def enable_buttons(self):
        self.log_message("Debug: Re-evaluating widget states.")
        try:
            if hasattr(self, 'min_outlets_entry') and self.min_outlets_entry.winfo_exists():
                self.min_outlets_entry.config(state='normal') # Start validation chain
            self.validate_inputs()

            params_valid = self.validate_parameters()
            data_ready = self.outlets_data is not None and not self.outlets_data.empty
            
            opt_state = 'normal' if params_valid and data_ready else 'disabled'
            if hasattr(self,'optimize_button') and self.optimize_button.winfo_exists() and self.optimize_button.cget('state') != opt_state:
                self.optimize_button.config(state=opt_state)

            sched_exportable = isinstance(self.schedule,dict) and self.schedule and any(isinstance(e,list) and e and any(isinstance(d,dict) and isinstance(d.get('RouteDF'),pd.DataFrame) and not d['RouteDF'].empty for d in e) for e in self.schedule.values())
            export_state = 'normal' if params_valid and data_ready and sched_exportable else 'disabled'
            if hasattr(self,'export_button') and self.export_button.winfo_exists() and self.export_button.cget('state') != export_state:
                self.export_button.config(state=export_state)
        except Exception as e: self.log_message(f"ERROR in enable_buttons: {e}")


    def log_message(self, message: str):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S"); log_entry = f"{timestamp} - {message}"
        print(log_entry)
        if hasattr(self,'queue') and self.queue: self.queue.put(('log_gui',log_entry))

    def _log_message_gui(self, message: str):
        try:
            if hasattr(self, 'results_text') and self.results_text.winfo_exists():
                max_lines=2500; num_lines=int(self.results_text.index('end-1c').split('.')[0])
                if num_lines > max_lines:
                    self.results_text.config(state='normal'); self.results_text.delete('1.0',f'{num_lines-max_lines+1}.0'); self.results_text.config(state='disabled')
                self.results_text.config(state='normal'); self.results_text.insert(tk.END,message+"\n"); self.results_text.see(tk.END); self.results_text.config(state='disabled')
        except: pass # Ignore GUI log errors

    def _show_error_box_gui(self, message: str):
        if hasattr(self,'root') and self.root.winfo_exists(): messagebox.showerror("Error",message,parent=self.root)
    def _show_warning_box_gui(self, message: str):
        if hasattr(self,'root') and self.root.winfo_exists(): messagebox.showwarning("Warning",message,parent=self.root)

    def setup_queue_checker(self):
        # (Identical to v2.29 - this handles GUI updates from threads)
        def check_queue_periodically():
            max_msgs = 20; processed_count = 0
            try:
                while processed_count < max_msgs:
                    if not hasattr(self,'queue') or self.queue is None: break
                    msg_type, payload = self.queue.get_nowait(); processed_count +=1
                    try:
                        if msg_type == 'log_gui': self._log_message_gui(str(payload))
                        elif msg_type == 'progress': self.progress_var.set(min(max(0.0,float(payload)),100.0))
                        elif msg_type == 'status': self.status_var.set(str(payload))
                        elif msg_type == 'update_est_reps': self.estimated_reps_var.set(f"Est. Reps: {payload}")
                        elif msg_type == 'error': self._show_error_box_gui(str(payload)); self.status_var.set("Error. Check log."); self.progress_var.set(0)
                        elif msg_type == 'warning': self._show_warning_box_gui(str(payload))
                        # Removed internal _show_xxx_box_gui as they are called directly
                        elif msg_type == 'ask_open_folder':
                            if os.path.isdir(str(payload)) and messagebox.askyesno("Export Complete", f"Results saved to:\n{payload}\n\nOpen folder?", parent=self.root): self._open_file_or_folder(str(payload))
                        elif msg_type == 'ask_open_map':
                            if os.path.isfile(str(payload)) and messagebox.askyesno("Map Ready", f"Map generated:\n{os.path.basename(str(payload))}\n\nOpen map?", parent=self.root): self._open_file_or_folder(str(payload))
                        elif msg_type == 'complete':
                            self.log_message(f"Debug: Queue received 'complete' for '{payload}'."); self.processing=False
                            current_status = self.status_var.get().lower()
                            if "error" not in current_status and "fail" not in current_status:
                                sched_has_data = isinstance(self.schedule, dict) and self.schedule and any(bool(e) for e in self.schedule.values())
                                if str(payload) == 'optimization': self.status_var.set("Optimization Complete. Ready to Export." if sched_has_data else "Optimization Complete (No schedule).")
                                elif str(payload) == 'export': self.status_var.set("Export Complete. Ready.")
                                else: self.status_var.set("Processing Complete. Ready.")
                            self.enable_buttons()
                        else: self.log_message(f"Warning: Unknown queue msg type: {msg_type}")
                    except Exception as e_inner: self.log_message(f"ERROR processing queue msg ({msg_type}): {e_inner}\n{traceback.format_exc()}")
                    finally:
                        if hasattr(self,'queue') and self.queue is not None:
                            try: self.queue.task_done()
                            except ValueError: pass # If already marked done
            except QueueEmpty: pass
            except Exception as e_outer: self.log_message(f"CRITICAL ERROR in queue checker: {e_outer}\n{traceback.format_exc()}")
            finally:
                if hasattr(self,'root') and self.root.winfo_exists(): self.root.after(100, check_queue_periodically)
        
        if hasattr(self,'root') and self.root.winfo_exists(): self.root.after(100, check_queue_periodically)

    def _open_file_or_folder(self, path: str):
        # (Identical to v2.29)
        current_os = platform.system()
        try:
            abs_path = os.path.abspath(path)
            if not os.path.exists(abs_path): self._show_error_box_gui(f"Path not found:\n{abs_path}"); return
            if current_os == "Windows": os.startfile(abs_path)
            elif current_os == "Darwin": subprocess.run(['open', abs_path], check=True)
            else: subprocess.run(['xdg-open', abs_path], check=True)
        except Exception as e: self._show_error_box_gui(f"Error opening '{abs_path}': {e}")

# --- Main Execution ---
if __name__ == "__main__":
    print(f"OR-Tools: {ortools.__version__}, Python: {sys.version.split(' ')[0]}")
    if not HAS_K_MEANS_CONSTRAINED: print("WARNING: k-means-constrained not found. Fallback K-Means clustering if needed.")
    app = RouteOptimizationApp()
    app.root.mainloop()